在schedule页面的顶部标签栏(tab)组件中存在响应式布局问题。在小屏幕设备上，标签栏的高度被压缩，导致底部的蓝色指示条(active indicator)无法正常显示。
具体问题表现：
- 正常显示的屏幕尺寸：430×932px、428×926px  
- 出现问题的屏幕尺寸：390×844px及更小尺寸
- 问题现象：标签栏底部的蓝色激活指示条消失或被裁切

请检查并修复schedule页面顶部标签栏组件的CSS样式，确保在小屏幕设备(特别是390×844px及以下)上能够正确显示底部的蓝色指示条。

技术背景：
- 使用TDesign小程序组件库的t-tabs组件
- 当前使用theme="line"主题和自定义样式类custom-top-tabs
- 可能涉及t-tabs__track、t-tabs__nav、t-tabs__item等内部样式类
- 需要考虑TDesign组件的默认指示器与自定义border-bottom指示器的冲突

需要调整的具体方面：
1. 检查.custom-top-tabs及其子元素的高度设置和overflow属性
2. 验证t-tabs__track指示器是否被display:none隐藏
3. 确保标签栏容器有足够的底部padding为指示条预留空间
4. 添加针对390px和375px断点的响应式媒体查询
5. 在小屏幕上增加指示条的厚度和可见性

请先使用codebase-retrieval工具定位schedule页面的相关样式文件(schedule.wxss)和组件代码(schedule.wxml)，分析当前的标签栏实现方式，然后提供具体的修复方案。