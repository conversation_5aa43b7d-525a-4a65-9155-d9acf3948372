/* user-management.wxss */
.container {
  padding: 16px 16px 0 16px;
  background-color: #f5f5f5;
  min-height: 100vh;
  height: 100vh;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  box-sizing: border-box;
  width: 100%;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
}

/* 顶部区域样式 */
.top-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 12px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  flex-shrink: 0;
  margin-bottom: 0;
}

.top-tabs-section {
  flex: 1;
  min-width: 0;
}

/* 顶部选项卡样式 */
.custom-top-tabs {
  /*
   * 背景 - 完全透明，让容器背景显示
   */
  background-color: transparent;
  border: none;

  /*
   * 移除圆角 - 线条选项卡不需要圆角
   */
  border-radius: 0;

  /*
   * 溢出控制
   */
  overflow: visible;

  /* 字体设置 */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  font-size: 16px;

  /*
   * 布局控制
   */
  margin: 0;
  width: 100%;
  height: auto;

  /*
   * 最小高度设置为最小值以确保紧凑布局
   */
  min-height: 1px;
  }

.custom-top-tabs .t-tabs__nav {
  /*
   * 内边距 - 优化的内边距
   */
  padding: 0;
  height: auto;
  min-height: 1px;

  /*
   * 移除底部边框，使用父容器的边框
   */
  border-bottom: none;

  /*
   * 布局优化
   */
  display: flex;
  align-items: center;

  /*
   * 背景渐变效果
   */
  background: transparent;
}

/**
 * 线条选项卡项目样式
 */
.custom-top-tabs .t-tabs__item {
  /*
   * 字体设置 - 优化可读性
   */
  font-size: 16px !important;
  font-weight: 500;

  /*
   * 内边距 - 增加舒适的点击区域
   */
  padding: 14px 20px !important;

  /*
   * 高度控制
   */
  height: auto;
  line-height: 1.4;
  min-height: 44px;

  /*
   * 布局控制
   */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;

  /*
   * 移除圆角和背景
   */
  border-radius: 0;
  background: transparent !important;

  /*
   * 底部边框 - 激活状态指示器
   */
  border-bottom: 3px solid transparent;

  /*
   * 过渡动画 - 更流畅的动画
   */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /*
   * 文字颜色
   */
  color: #666666 !important;

  /*
   * 相对定位用于伪元素
   */
  position: relative;
}

/*
 * 选项卡项目的装饰效果
 */
.custom-top-tabs .t-tabs__item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, #0052d9, transparent);
  transform: translateX(-50%);
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/**
 * 激活状态的线条选项卡
 */
.custom-top-tabs .t-tabs__item--active {
  /*
   * 文字颜色和字重
   */
  color: #0052d9 !important;
  font-weight: 600 !important;

  /*
   * 底部蓝色指示线 - 更粗更明显
   */
  border-bottom-color: #0052d9 !important;

  /*
   * 保持透明背景
   */
  background: transparent !important;

  /*
   * 文字阴影效果 - 增加层次感
   */
  text-shadow: 0 0 1px rgba(0, 82, 217, 0.1);
}

/*
 * 激活状态的顶部装饰线
 */
.custom-top-tabs .t-tabs__item--active::before {
  width: 60%; /* 激活时显示顶部装饰线 */
}

/**
 * 非激活状态的选项卡悬停效果
 */
.custom-top-tabs .t-tabs__item:not(.t-tabs__item--active):hover {
  /*
   * 悬停时的文字颜色
   */
  color: #333333 !important;

  /*
   * 保持透明背景
   */
  background: transparent !important;

  /*
   * 悬停时的底部边框效果
   */
  border-bottom-color: rgba(0, 82, 217, 0.3) !important;

  /*
   * 轻微的文字阴影
   */
  text-shadow: 0 0 1px rgba(51, 51, 51, 0.1);
}

/*
 * 悬停时的顶部装饰线
 */
.custom-top-tabs .t-tabs__item:not(.t-tabs__item--active):hover::before {
  width: 30%; /* 悬停时显示较短的顶部装饰线 */
}

/**
 * 底部指示线容器
 */
.custom-top-tabs .t-tabs__track {
  display: none; /* 隐藏默认的滑动指示器，使用border-bottom代替 */
}

/* 用户内容区域 */
.user-content {
  width: 100%;
  max-width: 700rpx;
  margin-left: auto;
  margin-right: auto;
  box-sizing: border-box;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  margin-top: 16px;
}

/* 用户列表样式 */
.user-list {
  margin-bottom: 0;
  width: 100%;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  box-sizing: border-box;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.user-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  box-sizing: border-box;
  width: 100%;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
}

.user-card:active {
  transform: scale(0.98);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.12);
}

/* 用户头部样式 */
.user-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 12px;
}

.user-avatar {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.user-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 500;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  display: inline-block;
  white-space: nowrap;
}

.user-status.student {
  background-color: #e6f3ff;
  color: #0052d9;
}

.user-status.coach {
  background-color: #e8f5e8;
  color: #52c41a;
}

.user-status.admin {
  background-color: #fff2e8;
  color: #fa8c16;
}

.user-status.other {
  background-color: #f0f0f0;
  color: #999;
}

/* 角色标签样式 */
.user-roles {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.role-tag {
  margin-right: 8px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid transparent;
}

.role-tag:last-child {
  margin-right: 0;
}

.role-tag:active {
  transform: scale(0.95);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
}

/* 用户信息列表样式 */
.user-info-list {
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 15px;
  color: #666;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  gap: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item t-icon {
  color: #0052d9;
  flex-shrink: 0;
}

.info-label {
  color: #888;
  min-width: 90px;
  flex-shrink: 0;
  font-size: 15px;
}

.info-item text:last-child {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.copyable-text {
  color: #0052d9;
  text-decoration: underline;
  cursor: pointer;
  transition: color 0.2s ease;
}

.copyable-text:active {
  color: #003ba3;
}

/* 用户底部样式 */
.user-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  gap: 8px;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-wrap: wrap;
  overflow-x: auto;
  padding-bottom: 2px;
  min-width: 0;
  width: 100%;
}

.action-buttons .t-button {
  flex-shrink: 0;
  min-width: 60px;
  max-width: 80px;
  margin: 2px;
}

/* 加载和结束指示器 */
.loading-indicator,
.end-indicator {
  text-align: center;
  padding: 10px;
  color: #999;
  font-size: 15px;
}

/* 固定卡片宽度，防止内容拉伸 */
.user-card {
  width: 90vw;
  max-width: 700rpx;
  min-width: 320rpx;
  margin-left: auto;
  margin-right: auto;
  box-sizing: border-box;
}

/* 改角色弹窗样式 */
.edit-role-popup {
  background: #ffffff;
  border-radius: 16px;
  width: 80vw;
  max-width: 400px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.popup-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.close-icon {
  color: #999;
  cursor: pointer;
  transition: color 0.2s ease;
}

.close-icon:active {
  color: #666;
}

.popup-content {
  padding: 20px 24px;
}

.user-info-section {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.user-name {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.user-id {
  display: block;
  font-size: 13px;
  color: #999;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.role-section {
  margin: 20px 0;
}

.section-title {
  display: block;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.section-tip {
  display: block;
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
  line-height: 1.4;
}

.role-checkboxes {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.role-checkbox-item {
  padding: 8px 0;
}

/* 禁用状态的复选框样式 */
.role-checkbox-item .t-checkbox--disabled {
  opacity: 0.6;
}

.role-checkbox-item .t-checkbox--disabled .t-checkbox__label {
  color: #999;
}

/* 讲师信息弹窗样式 */
.coach-info-popup {
  width: 90vw;
  max-width: 600px;
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
}

.coach-info-popup .popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.coach-info-popup .popup-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.coach-info-popup .close-icon {
  color: #999;
  cursor: pointer;
}

.coach-info-popup .popup-content {
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;
}

.coach-info-popup .form-section {
  margin-top: 20px;
}

.coach-info-popup .form-item {
  margin-bottom: 20px;
}

.coach-info-popup .form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.coach-info-popup .popup-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #f0f0f0;
}

/* 改角色弹窗底部按钮样式 */
.edit-role-popup .popup-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #f0f0f0;
}

/* 搜索栏样式 */
.search-actions-section {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: nowrap;
  justify-content: flex-start;
  box-sizing: border-box;
  overflow: visible;
  min-height: 44px; /* 与t-tabs的高度保持一致 */

  /*
   * 背景设计 - 与t-tabs的渐变背景保持一致
   */
  background: linear-gradient(180deg, #ffffff 0%, #fafbfc 100%);

  /*
   * 边框设计 - 与t-tabs的精致边框保持一致
   */
  border: 1px solid #f0f0f0;
  border-radius: 8px;

  /*
   * 阴影效果 - 与t-tabs的轻微阴影保持一致
   */
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.02),
    0 0 0 1px rgba(0, 0, 0, 0.01);

  /*
   * 内边距 - 适当的内边距
   */
  padding: 6px 16px;

  /*
   * 过渡动画 - 与t-tabs保持一致
   */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.search-actions-section.collapsed {
  justify-content: flex-start;
}

.collapsed-layout {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.search-icon-only {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.search-icon-only:active {
  background: #f0f8ff;
  border-color: #0052d9;
  transform: scale(0.95);
}

.search-toggle-icon {
  color: #666666;
  font-size: 14px;
}

.search-actions-section.expanded {
  justify-content: flex-start;
}

.expanded-layout {
  width: 100%;
  height: 100%;
  display: flex;
  flex: 1;
}

.search-input-container {
  display: flex;
  align-items: center;
  background: #ffffff;
  border-radius: 6px;
  padding: 8px 12px;
  border: 1px solid #e7e7e7;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: searchExpand 0.3s ease-out;
  width: 100%;
  flex: 1;
  box-sizing: border-box;
  height: 36px;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.02);
}

.search-input-container:focus-within {
  border-color: #0052d9;
  box-shadow:
    0 0 0 2px rgba(0, 82, 217, 0.1),
    inset 0 1px 2px rgba(0, 0, 0, 0.02);
}

.search-icon {
  color: #0052d9;
  margin-right: 8px;
  flex-shrink: 0;
  font-size: 16px;
  opacity: 0.8;
}

.search-input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 15px;
  color: #333333;
  background: transparent;
  min-width: 0;
  height: 20px;
  line-height: 20px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  font-weight: 400;
}

.search-input::placeholder {
  color: #999999;
  font-size: 15px;
  font-weight: 400;
}

.clear-icon,
.collapse-icon {
  color: #999999;
  margin-left: 8px;
  flex-shrink: 0;
  cursor: pointer;
  font-size: 16px;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0.8;
}

.clear-icon:active,
.collapse-icon:active {
  color: #0052d9;
  background: rgba(0, 82, 217, 0.1);
  opacity: 1;
  transform: scale(0.95);
}

@keyframes searchExpand {
  from {
    opacity: 0;
    transform: scaleX(0.8);
  }
  to {
    opacity: 1;
    transform: scaleX(1);
  }
}

/* 响应式布局调整 */
/* 针对小屏幕进行优化，确保标签栏在不同尺寸下都能正确显示 */
@media (max-width: 390px) {
  .custom-top-tabs .t-tabs__item {
    padding: 12px 14px !important; /* 减小水平内边距 */
    font-size: 15px !important; /* 缩小字体 */
    min-height: 42px; /* 减小最小高度 */
  }
}

@media (max-width: 375px) {
  .container {
    padding: 8px;
  }

  .user-card {
    padding: 12px;
  }

  .user-title {
    font-size: 16px;
  }

  .info-item {
    font-size: 13px;
  }

  .info-label {
    font-size: 13px;
  }

  .action-buttons .t-button {
    font-size: 12px;
    padding: 0 8px;
  }

  .custom-top-tabs .t-tabs__item {
    padding: 12px 12px !important; /* 进一步减小内边距 */
    font-size: 15px !important;
  }
}
