/* 页面容器样式 - 渐变背景营造高级感 */
.membership-card-page {
  min-height: 100vh;
  /*
   * 渐变背景：从浅灰到更浅的灰，模拟真实环境光影
   * linear-gradient: CSS渐变函数，创建平滑的颜色过渡
   * 135deg: 渐变角度，从左上到右下的对角线渐变
   */
  background: linear-gradient(135deg, #f0f2f5 0%, #fafbfc 100%);
  padding-bottom: 120rpx;
  padding-top: 40rpx;
}

/* 卡片列表容器 */
.card-list {
  display: flex;
  flex-direction: column;
  gap: 40rpx; /* 增加卡片间距，让每张卡更突出 */
  margin: 0 32rpx;
  padding-top: 20rpx;
}

/* 考勤卡片样式 - 真实卡片设计 */
.membership-card {
  /*
   * 渐变背景：模拟真实卡片的质感
   * 从纯白到微微的暖白色，增加层次感
   */
  background: linear-gradient(145deg, #ffffff 0%, #fefefe 50%, #fdfdfd 100%);

  /*
   * border-radius: 圆角设计
   * 16rpx: 比原来更大的圆角，更接近真实卡片
   */
  border-radius: 16rpx;

  /*
   * 多层阴影：模拟真实卡片的立体效果
   * 第一层：主要阴影，模拟卡片悬浮
   * 第二层：边缘阴影，增加深度
   * 第三层：细微阴影，增加质感
   */
  box-shadow:
    0 8rpx 32rpx rgba(0, 0, 0, 0.12),
    0 4rpx 16rpx rgba(0, 0, 0, 0.08),
    0 2rpx 8rpx rgba(0, 0, 0, 0.04);

  padding: 40rpx 32rpx; /* 增加内边距，让内容更舒适 */
  position: relative;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;

  /*
   * transition: 过渡动画
   * 为悬停和点击效果提供平滑过渡
   */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /*
   * 边框：微妙的边框增加卡片边界感
   * rgba(255, 255, 255, 0.8): 半透明白色边框，增加高光效果
   */
  border: 1rpx solid rgba(255, 255, 255, 0.8);

  /*
   * 卡片装饰：添加微妙的内阴影，模拟卡片厚度
   */
  box-shadow:
    0 8rpx 32rpx rgba(0, 0, 0, 0.12),
    0 4rpx 16rpx rgba(0, 0, 0, 0.08),
    0 2rpx 8rpx rgba(0, 0, 0, 0.04),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.6);
}

/* 卡片悬停效果 - 增强立体感 */
.membership-card:hover {
  /*
   * transform: 变换效果
   * translateY(-4rpx): 轻微上移，模拟卡片被拿起
   */
  transform: translateY(-4rpx);

  /*
   * 悬停时的阴影：更强烈的阴影效果
   */
  box-shadow:
    0 16rpx 48rpx rgba(0, 0, 0, 0.15),
    0 8rpx 24rpx rgba(0, 0, 0, 0.1),
    0 4rpx 12rpx rgba(0, 0, 0, 0.06),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
}

/* 卡片点击效果 - 模拟按压 */
.membership-card:active {
  /*
   * transform: 组合变换
   * scale(0.98): 轻微缩小，模拟按压效果
   * translateY(-2rpx): 减少上移距离
   */
  transform: scale(0.98) translateY(-2rpx);

  /*
   * 点击时的阴影：减弱阴影，模拟按压
   */
  box-shadow:
    0 4rpx 16rpx rgba(0, 0, 0, 0.1),
    0 2rpx 8rpx rgba(0, 0, 0, 0.06),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.4);
}

/* 卡片装饰性背景 - 模拟真实卡片的设计元素 */
.card-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none; /* 不影响点击事件 */
  overflow: hidden;
  border-radius: 16rpx;
  z-index: 0;
}

/* 装饰图案 - 添加微妙的几何图案 */
.decoration-pattern {
  position: absolute;
  top: -50rpx;
  right: -50rpx;
  width: 200rpx;
  height: 200rpx;

  /*
   * 圆形装饰：使用径向渐变创建圆形图案
   */
  background: radial-gradient(circle,
    rgba(0, 82, 217, 0.03) 0%,
    rgba(0, 82, 217, 0.01) 40%,
    transparent 70%);
  border-radius: 50%;
}

/* 装饰图案的伪元素 - 添加更多层次 */
.decoration-pattern::before {
  content: '';
  position: absolute;
  top: 30rpx;
  left: 30rpx;
  width: 140rpx;
  height: 140rpx;
  background: radial-gradient(circle,
    rgba(0, 82, 217, 0.02) 0%,
    transparent 60%);
  border-radius: 50%;
}

/* 卡片类型标识 - 右上角的装饰性文字 */
.decoration-logo {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  font-size: 20rpx;
  font-weight: 300;
  color: rgba(0, 82, 217, 0.15);
  letter-spacing: 2rpx;
  transform: rotate(15deg); /* 轻微旋转增加设计感 */

  /*
   * 文字装饰：添加微妙的描边效果
   */
  text-shadow:
    1rpx 1rpx 0 rgba(255, 255, 255, 0.8),
    -1rpx -1rpx 0 rgba(0, 82, 217, 0.05);
}

/* 卡片头部样式 - 模拟真实卡片的标题区域 */
.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx; /* 增加底部间距 */
  padding-bottom: 24rpx; /* 增加内边距 */
  position: relative;

  /*
   * 装饰性边框：使用渐变边框模拟卡片分割线
   * linear-gradient: 从透明到浅灰再到透明的渐变
   */
  border-bottom: 2rpx solid transparent;
  background:
    linear-gradient(90deg, transparent 0%, #e8eaed 20%, #e8eaed 80%, transparent 100%)
    bottom/100% 1rpx no-repeat;
}

/* 卡片头部装饰 - 添加微妙的背景渐变 */
.card-header::before {
  content: '';
  position: absolute;
  top: -20rpx;
  left: -32rpx;
  right: -32rpx;
  height: 80rpx;
  /*
   * 头部装饰渐变：模拟卡片顶部的光泽效果
   */
  background: linear-gradient(180deg,
    rgba(255, 255, 255, 0.6) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    transparent 100%);
  border-radius: 16rpx 16rpx 0 0;
  z-index: 0;
}

/* 确保头部内容在装饰层之上 */
.card-header > * {
  position: relative;
  z-index: 1;
}

/* 卡片图标样式 - 增强视觉效果 */
.card-header t-icon {
  /*
   * 渐变色彩：从深蓝到浅蓝的渐变，增加立体感
   * 注意：小程序中图标颜色可能不支持渐变，这里使用深蓝色
   */
  color: #0052d9;
  margin-right: 16rpx; /* 增加右边距 */

  /*
   * 图标装饰：添加微妙的阴影效果
   */
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 82, 217, 0.2));
}

/* 卡号样式 - 模拟真实卡片的卡号字体 */
.card-number {
  font-size: 36rpx; /* 增大字体 */
  font-weight: 700; /* 加粗字体 */
  color: #1a1a1a; /* 更深的颜色 */
  flex: 1;

  /*
   * 字体装饰：添加微妙的文字阴影，增加立体感
   */
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);

  /*
   * 字母间距：模拟卡号的专业排版
   */
  letter-spacing: 1rpx;
}

/* 状态标签样式 - 增强视觉效果 */
.card-header t-tag {
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  font-size: 24rpx; /* 稍微增大字体 */
  font-weight: 600; /* 加粗字体 */
  border-radius: 12rpx; /* 增加圆角 */
  flex-shrink: 0;
  white-space: nowrap;

  /*
   * 标签装饰：添加微妙的阴影和边框
   */
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

/* 卡片主体内容样式 - 模拟真实卡片的信息区域 */
.card-body {
  display: flex;
  flex-direction: column;
  gap: 20rpx; /* 增加行间距 */
  position: relative;

  /*
   * 主体装饰：添加微妙的背景，模拟卡片信息区域
   */
  background: linear-gradient(180deg,
    rgba(248, 249, 250, 0.3) 0%,
    rgba(255, 255, 255, 0.1) 100%);
  border-radius: 8rpx;
  padding: 24rpx 20rpx; /* 添加内边距 */
  margin: 0 -20rpx; /* 负边距让背景延伸到卡片边缘 */
}

/* 信息行样式 - 增强可读性和美观度 */
.card-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 28rpx;
  color: #666;
  padding: 12rpx 16rpx; /* 增加内边距 */
  position: relative;

  /*
   * 行装饰：添加微妙的背景和圆角
   */
  background: rgba(255, 255, 255, 0.4);
  border-radius: 8rpx;

  /*
   * 过渡动画：为悬停效果提供平滑过渡
   */
  transition: all 0.2s ease;
}

/* 信息行悬停效果 */
.card-row:hover {
  background: rgba(255, 255, 255, 0.6);
  transform: translateX(4rpx); /* 轻微右移 */
}

/* 最后一行样式调整 */
.card-row:last-child {
  margin-bottom: 0;
}

/* 标签文字样式 - 增强层次感 */
.card-row text:first-child,
.card-row .label {
  color: #666; /* 调整颜色，增加对比度 */
  font-weight: 600; /* 加粗字体 */
  font-size: 26rpx;
  min-width: 140rpx; /* 增加最小宽度 */

  /*
   * 标签装饰：添加微妙的文字效果
   */
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
}

/* 数值文字样式 - 突出重要信息 */
.card-row text:last-child,
.card-row .value {
  color: #1a1a1a; /* 更深的颜色 */
  font-weight: 600; /* 加粗字体 */
  font-size: 30rpx; /* 增大字体 */
  text-align: right;
  flex: 1;

  /*
   * 数值装饰：添加微妙的文字阴影
   */
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* 重要数值高亮样式 - 剩余次数特殊处理 */
.card-row .value.highlight {
  /*
   * 渐变文字色彩：从深蓝到浅蓝的渐变效果
   * 注意：小程序可能不支持文字渐变，这里使用深蓝色
   */
  color: #0052d9;
  font-weight: 700; /* 更粗的字体 */
  font-size: 34rpx; /* 更大的字体 */

  /*
   * 高亮装饰：添加发光效果
   */
  text-shadow:
    0 0 8rpx rgba(0, 82, 217, 0.3),
    0 2rpx 4rpx rgba(0, 82, 217, 0.2);

  /*
   * 背景装饰：添加微妙的背景高亮
   */
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(0, 82, 217, 0.05) 50%,
    transparent 100%);
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}
/* 空状态样式 */
.membership-card-page t-empty {
  margin-top: 120rpx;
  background: #fff;
  border-radius: 12rpx;
  padding: 60rpx 40rpx;
  margin-left: 32rpx;
  margin-right: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

/* TabBar占位符 */
#tab-bar-placeholder {
  height: 120rpx;
}